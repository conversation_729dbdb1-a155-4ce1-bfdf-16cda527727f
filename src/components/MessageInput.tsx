import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useApp } from '@/lib/app-context';
import { Send, Plus, Smile, AtSign, Type, Maximize2, Minimize2 } from 'lucide-react';
import SimpleMDEEditor from 'react-simplemde-editor';
import 'easymde/dist/easymde.min.css';
import type { Options } from 'easymde';
import EasyMDE from 'easymde';
import { LocalAttachment } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { AttachmentPill } from './AttachmentPill'; // Import AttachmentPill

interface MessageInputProps {
  threadId?: string;
  placeholder?: string;
  autoFocus?: boolean;
  topicId?: string;
  disabled?: boolean;
}

export const MessageInput = ({
  threadId,
  placeholder = "Message",
  autoFocus = false,
  topicId,
  disabled = false
}: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const [pendingAttachments, setPendingAttachments] = useState<LocalAttachment[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showToolbar, setShowToolbar] = useState(false);
  const { sendMessage, currentChannel, currentDirectMessage } = useApp();
  const editorInstanceRef = useRef<EasyMDE | null>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateImageName = (file: File): string => {
    // If the file has a meaningful name (not generic), use it
    if (file.name && file.name !== 'image.png' && file.name !== 'image' && !file.name.startsWith('blob:')) {
      return file.name;
    }

    // Generate a better name for pasted/generic images
    const now = new Date();
    const timeStr = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/:/g, '-');
    const dateStr = now.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
    const extension = file.type.split('/')[1] || 'png';
    return `Screenshot ${dateStr} at ${timeStr}.${extension}`;
  };

  const addFileAsAttachment = async (file: File) => {
    const localId = uuidv4();
    const fileName = file.type.startsWith('image/') ? generateImageName(file) : file.name;

    const newAttachmentBase: Omit<LocalAttachment, 'dataUrl' | 'textContent'> = {
      id: localId,
      name: fileName,
      type: file.type,
      size: file.size,
      fileObject: file,
    };

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, dataUrl: e.target?.result as string }]);
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('text/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, textContent: e.target?.result as string }]);
      };
      reader.readAsText(file);
    } else {
      console.warn(`File type ${file.type} not directly supported for inline content. Will be handled by upload later.`);
      setPendingAttachments(prev => [...prev, { ...newAttachmentBase }]);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setPendingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const renameAttachment = (attachmentId: string, newName: string) => {
    setPendingAttachments(prev =>
      prev.map(att =>
        att.id === attachmentId ? { ...att, name: newName } : att
      )
    );
  };

  const handleSendMessage = () => {
    if ((!message.trim() && pendingAttachments.length === 0) || disabled) return;

    if (topicId) {
      sendMessage(message, currentChannel?.id, undefined, threadId, topicId, pendingAttachments);
    } else {
      sendMessage(message, currentChannel?.id, currentDirectMessage?.id, threadId, undefined, pendingAttachments);
    }
    setMessage('');
    setPendingAttachments([]);
    editorInstanceRef.current?.codemirror.focus();
  };

  const handleEditorKeyDown = (_instance: any, event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      editorInstanceRef.current?.codemirror.getInputField().blur();
    }
  };

  const handlePaste = (_instance: any, event: ClipboardEvent) => {
    if (event.clipboardData) {
      for (let i = 0; i < event.clipboardData.items.length; i++) {
        const item = event.clipboardData.items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            event.preventDefault();
            addFileAsAttachment(file);
          }
        }
      }
    }
  };

  // Toolbar functions
  const toggleBold = () => {
    const cm = editorInstanceRef.current?.codemirror;
    if (cm) {
      const selection = cm.getSelection();
      if (selection.startsWith('**') && selection.endsWith('**')) {
        cm.replaceSelection(selection.slice(2, -2));
      } else {
        cm.replaceSelection(`**${selection}**`);
      }
      cm.focus();
    }
  };

  const toggleItalic = () => {
    const cm = editorInstanceRef.current?.codemirror;
    if (cm) {
      const selection = cm.getSelection();
      if (selection.startsWith('*') && selection.endsWith('*') && !selection.startsWith('**')) {
        cm.replaceSelection(selection.slice(1, -1));
      } else {
        cm.replaceSelection(`*${selection}*`);
      }
      cm.focus();
    }
  };

  const toggleCode = () => {
    const cm = editorInstanceRef.current?.codemirror;
    if (cm) {
      const selection = cm.getSelection();
      if (selection.startsWith('`') && selection.endsWith('`')) {
        cm.replaceSelection(selection.slice(1, -1));
      } else {
        cm.replaceSelection(`\`${selection}\``);
      }
      cm.focus();
    }
  };

  const toggleList = () => {
    const cm = editorInstanceRef.current?.codemirror;
    if (cm) {
      const cursor = cm.getCursor();
      const line = cm.getLine(cursor.line);
      if (line.startsWith('- ')) {
        cm.replaceRange('', { line: cursor.line, ch: 0 }, { line: cursor.line, ch: 2 });
      } else {
        cm.replaceRange('- ', { line: cursor.line, ch: 0 });
      }
      cm.focus();
    }
  };

  const toggleOrderedList = () => {
    const cm = editorInstanceRef.current?.codemirror;
    if (cm) {
      const cursor = cm.getCursor();
      const line = cm.getLine(cursor.line);
      const match = line.match(/^(\d+)\. /);
      if (match) {
        cm.replaceRange('', { line: cursor.line, ch: 0 }, { line: cursor.line, ch: match[0].length });
      } else {
        cm.replaceRange('1. ', { line: cursor.line, ch: 0 });
      }
      cm.focus();
    }
  };

  // Dynamic placeholder based on context
  const getPlaceholder = () => {
    if (disabled) return "Cannot send messages to an archived topic";
    if (threadId) return "Reply in thread...";
    if (topicId) return "Message in topic...";
    if (currentChannel) return `Message #${currentChannel.name}`;
    if (currentDirectMessage) return "Message";
    return placeholder;
  };

  const simpleMdeOptions: Options = useMemo(() => {
    return {
      autofocus: autoFocus,
      placeholder: getPlaceholder(),
      toolbar: showToolbar ? [
        {
          name: "bold",
          action: toggleBold,
          className: "fa fa-bold",
          title: "Bold",
        },
        {
          name: "italic",
          action: toggleItalic,
          className: "fa fa-italic",
          title: "Italic",
        },
        {
          name: "code",
          action: toggleCode,
          className: "fa fa-code",
          title: "Code",
        },
        "|",
        {
          name: "unordered-list",
          action: toggleList,
          className: "fa fa-list-ul",
          title: "Bullet List",
        },
        {
          name: "ordered-list",
          action: toggleOrderedList,
          className: "fa fa-list-ol",
          title: "Numbered List",
        },
      ] : false,
      status: false,
      spellChecker: false,
      minHeight: isExpanded ? "300px" : "20px",
      maxHeight: isExpanded ? "80vh" : "150px",
      autoDownloadFontAwesome: false,
      // Disable conflicting shortcuts to prevent interference with global app shortcuts
      shortcuts: {
        toggleBold: null,
        toggleItalic: null,
        toggleStrikethrough: null,
        toggleCodeBlock: null,
        toggleHeadingSmaller: null,
        toggleHeadingBigger: null,
        toggleHeading1: null,
        toggleHeading2: null,
        toggleHeading3: null,
        toggleUnorderedList: null,
        toggleOrderedList: null,
        cleanBlock: null,
        drawLink: null,
        drawImage: null,
        drawTable: null,
        drawHorizontalRule: null,
        undo: null,
        redo: null,
        togglePreview: null,
        toggleSideBySide: null,
        toggleFullScreen: null
      }
    };
  }, [autoFocus, placeholder, disabled, showToolbar, isExpanded, threadId, topicId, currentChannel, currentDirectMessage]);

  useEffect(() => {
    if (autoFocus && editorInstanceRef.current) {
      setTimeout(() => editorInstanceRef.current?.codemirror.focus(), 0);
    }
  }, [autoFocus, currentChannel, currentDirectMessage]);

  // Handle escape key to close expanded mode
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isExpanded) {
        setIsExpanded(false);
        e.preventDefault();
      }
    };

    if (isExpanded) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isExpanded]);

  // Auto-show toolbar when typing formatting characters
  useEffect(() => {
    if (message.includes('**') || message.includes('*') || message.includes('`') || message.includes('- ') || message.includes('1. ')) {
      setShowToolbar(true);
    }
  }, [message]);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      Array.from(event.dataTransfer.files).forEach(file => {
        addFileAsAttachment(file);
      });
      event.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      Array.from(event.target.files).forEach(file => {
        addFileAsAttachment(file);
      });
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div
      ref={dropZoneRef}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      className={`message-input-container relative flex flex-col border border-[var(--app-border)] rounded-lg bg-[var(--app-main-bg)] shadow-sm transition-all duration-200 ${
        isExpanded ? 'fixed inset-4 z-50 shadow-2xl' : ''
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {/* Expanded mode header */}
      {isExpanded && (
        <div className="flex items-center justify-between p-3 border-b border-[var(--app-border)] bg-[var(--app-main-bg)]">
          <div className="flex items-center space-x-2">
            <h3 className="text-sm font-medium text-[var(--app-main-text)]">
              {threadId ? 'Reply in thread' : topicId ? 'Message in topic' : currentChannel ? `Message #${currentChannel.name}` : 'New message'}
            </h3>
          </div>
          <button
            onClick={() => setIsExpanded(false)}
            className="p-1 rounded hover:bg-[var(--app-hover-bg)] text-[var(--app-main-text)]"
            aria-label="Minimize"
          >
            <Minimize2 size={16} />
          </button>
        </div>
      )}

      {/* Main editor area */}
      <div className={`flex-1 flex flex-col ${isExpanded ? 'min-h-0' : ''}`}>
        <div className={`message-input-editor-wrapper flex-1 ${isExpanded ? 'p-3 expanded' : 'p-2'}`}>
          <SimpleMDEEditor
            value={message}
            onChange={setMessage}
            options={simpleMdeOptions}
            getMdeInstance={(instance) => {
              editorInstanceRef.current = instance;
            }}
            events={{
              keydown: handleEditorKeyDown,
              paste: handlePaste,
            }}
          />
        </div>

        {/* Attachments area - only show if there are attachments */}
        {pendingAttachments.length > 0 && (
          <div className="message-input-attachments px-3 py-2 border-t border-[var(--app-border)] flex flex-wrap gap-2">
            {pendingAttachments.map(att => (
              <AttachmentPill key={att.id} attachment={att} onRemove={removeAttachment} onRename={renameAttachment} />
            ))}
          </div>
        )}

        {/* Bottom toolbar */}
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center space-x-1">
            {/* File attachment button */}
            <input
              type="file"
              multiple
              ref={fileInputRef}
              onChange={handleFileSelect}
              className="hidden"
              accept="image/*,text/plain,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar"
            />
            <button
              className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'
              }`}
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled}
              aria-label="Attach file"
              title="Attach files"
            >
              <Plus size={18} />
            </button>

            {/* Emoji button */}
            <button
              className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'
              }`}
              disabled={disabled}
              aria-label="Add emoji"
              title="Add emoji"
            >
              <Smile size={18} />
            </button>

            {/* Mention button */}
            <button
              className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'
              }`}
              disabled={disabled}
              aria-label="Mention someone"
              title="Mention someone"
              onClick={() => {
                const cm = editorInstanceRef.current?.codemirror;
                if (cm) {
                  cm.replaceSelection('@');
                  cm.focus();
                }
              }}
            >
              <AtSign size={18} />
            </button>

            {/* Formatting toolbar toggle */}
            <button
              className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                showToolbar ? 'bg-[var(--app-hover-bg)]' : ''
              } ${disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'}`}
              onClick={() => setShowToolbar(!showToolbar)}
              disabled={disabled}
              aria-label="Toggle formatting"
              title="Toggle formatting toolbar"
            >
              <Type size={18} />
            </button>

            {/* Expand button - moved here */}
            {!isExpanded && (
              <button
                className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                  disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'
                }`}
                onClick={() => setIsExpanded(true)}
                disabled={disabled}
                aria-label="Expand editor"
                title="Expand editor"
              >
                <Maximize2 size={18} />
              </button>
            )}
          </div>

          <div className="flex items-center space-x-1">
            {/* Character count for long messages */}
            {message.length > 500 && (
              <span className="text-xs text-[var(--app-main-text)] opacity-60 mr-2">
                {message.length}
              </span>
            )}

            {/* Expand button */}
            {!isExpanded && (
              <button
                className={`message-input-toolbar-btn p-1.5 rounded hover:bg-[var(--app-hover-bg)] transition-colors ${
                  disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'
                }`}
                onClick={() => setIsExpanded(true)}
                disabled={disabled}
                aria-label="Expand editor"
                title="Expand editor"
              >
                <Maximize2 size={18} />
              </button>
            )}

            {/* Send button */}
            <button
              className={`message-input-send-btn p-1.5 rounded transition-colors ${
                (message.trim() || pendingAttachments.length > 0) && !disabled
                  ? 'text-[var(--app-highlight)] hover:bg-[var(--app-highlight)] hover:text-white'
                  : 'text-[var(--app-main-text)] opacity-30'
              } ${disabled ? 'cursor-not-allowed' : ''}`}
              onClick={handleSendMessage}
              disabled={(!message.trim() && pendingAttachments.length === 0) || disabled}
              aria-label="Send message"
              title="Send message"
            >
              <Send size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
